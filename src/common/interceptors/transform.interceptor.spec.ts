import { Test, TestingModule } from '@nestjs/testing';
import { ExecutionContext, CallHandler } from '@nestjs/common';
import { of } from 'rxjs';
import { TransformInterceptor, Response } from './transform.interceptor';

describe('TransformInterceptor', () => {
  let interceptor: TransformInterceptor<any>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [TransformInterceptor],
    }).compile();

    interceptor = module.get<TransformInterceptor<any>>(TransformInterceptor);
  });

  it('should be defined', () => {
    expect(interceptor).toBeDefined();
  });

  describe('intercept', () => {
    let mockExecutionContext: Partial<ExecutionContext>;
    let mockCallHandler: Partial<CallHandler>;
    let mockResponse: any;

    beforeEach(() => {
      mockResponse = {
        statusCode: 200,
      };

      mockExecutionContext = {
        switchToHttp: jest.fn().mockReturnValue({
          getResponse: () => mockResponse,
        }),
      };

      mockCallHandler = {
        handle: jest.fn(),
      };
    });

    it('should transform response with data, statusCode, and timestamp', (done) => {
      // Arrange
      const testData = { id: 1, name: 'test user' };
      mockCallHandler.handle = jest.fn().mockReturnValue(of(testData));

      // Mock Date to control timestamp
      const mockDate = new Date('2023-01-01T12:00:00.000Z');
      const originalDate = global.Date;
      global.Date = jest.fn(() => mockDate) as any;
      global.Date.prototype = originalDate.prototype;

      // Act
      const result = interceptor.intercept(
        mockExecutionContext as ExecutionContext,
        mockCallHandler as CallHandler,
      );

      // Assert
      result.subscribe((transformedData: Response<any>) => {
        expect(transformedData).toEqual({
          data: testData,
          statusCode: 200,
          timestamp: '2023-01-01T12:00:00.000Z',
        });

        // Restore Date
        global.Date = originalDate;
        done();
      });
    });

    it('should handle null data', (done) => {
      // Arrange
      mockCallHandler.handle = jest.fn().mockReturnValue(of(null));

      // Act
      const result = interceptor.intercept(
        mockExecutionContext as ExecutionContext,
        mockCallHandler as CallHandler,
      );

      // Assert
      result.subscribe((transformedData: Response<any>) => {
        expect(transformedData.data).toBeNull();
        expect(transformedData.statusCode).toBe(200);
        expect(transformedData.timestamp).toBeDefined();
        done();
      });
    });

    it('should handle undefined data', (done) => {
      // Arrange
      mockCallHandler.handle = jest.fn().mockReturnValue(of(undefined));

      // Act
      const result = interceptor.intercept(
        mockExecutionContext as ExecutionContext,
        mockCallHandler as CallHandler,
      );

      // Assert
      result.subscribe((transformedData: Response<any>) => {
        expect(transformedData.data).toBeUndefined();
        expect(transformedData.statusCode).toBe(200);
        expect(transformedData.timestamp).toBeDefined();
        done();
      });
    });

    it('should handle array data', (done) => {
      // Arrange
      const testData = [
        { id: 1, name: 'user1' },
        { id: 2, name: 'user2' },
      ];
      mockCallHandler.handle = jest.fn().mockReturnValue(of(testData));

      // Act
      const result = interceptor.intercept(
        mockExecutionContext as ExecutionContext,
        mockCallHandler as CallHandler,
      );

      // Assert
      result.subscribe((transformedData: Response<any>) => {
        expect(transformedData.data).toEqual(testData);
        expect(transformedData.statusCode).toBe(200);
        expect(transformedData.timestamp).toBeDefined();
        done();
      });
    });

    it('should handle string data', (done) => {
      // Arrange
      const testData = 'Hello World';
      mockCallHandler.handle = jest.fn().mockReturnValue(of(testData));

      // Act
      const result = interceptor.intercept(
        mockExecutionContext as ExecutionContext,
        mockCallHandler as CallHandler,
      );

      // Assert
      result.subscribe((transformedData: Response<any>) => {
        expect(transformedData.data).toBe(testData);
        expect(transformedData.statusCode).toBe(200);
        expect(transformedData.timestamp).toBeDefined();
        done();
      });
    });

    it('should handle number data', (done) => {
      // Arrange
      const testData = 42;
      mockCallHandler.handle = jest.fn().mockReturnValue(of(testData));

      // Act
      const result = interceptor.intercept(
        mockExecutionContext as ExecutionContext,
        mockCallHandler as CallHandler,
      );

      // Assert
      result.subscribe((transformedData: Response<any>) => {
        expect(transformedData.data).toBe(testData);
        expect(transformedData.statusCode).toBe(200);
        expect(transformedData.timestamp).toBeDefined();
        done();
      });
    });

    it('should handle boolean data', (done) => {
      // Arrange
      const testData = true;
      mockCallHandler.handle = jest.fn().mockReturnValue(of(testData));

      // Act
      const result = interceptor.intercept(
        mockExecutionContext as ExecutionContext,
        mockCallHandler as CallHandler,
      );

      // Assert
      result.subscribe((transformedData: Response<any>) => {
        expect(transformedData.data).toBe(testData);
        expect(transformedData.statusCode).toBe(200);
        expect(transformedData.timestamp).toBeDefined();
        done();
      });
    });

    it('should use correct status code from response', (done) => {
      // Arrange
      mockResponse.statusCode = 201;
      const testData = { id: 1, created: true };
      mockCallHandler.handle = jest.fn().mockReturnValue(of(testData));

      // Act
      const result = interceptor.intercept(
        mockExecutionContext as ExecutionContext,
        mockCallHandler as CallHandler,
      );

      // Assert
      result.subscribe((transformedData: Response<any>) => {
        expect(transformedData.statusCode).toBe(201);
        expect(transformedData.data).toEqual(testData);
        done();
      });
    });

    it('should handle different status codes', (done) => {
      // Arrange
      mockResponse.statusCode = 404;
      const testData = { error: 'Not found' };
      mockCallHandler.handle = jest.fn().mockReturnValue(of(testData));

      // Act
      const result = interceptor.intercept(
        mockExecutionContext as ExecutionContext,
        mockCallHandler as CallHandler,
      );

      // Assert
      result.subscribe((transformedData: Response<any>) => {
        expect(transformedData.statusCode).toBe(404);
        expect(transformedData.data).toEqual(testData);
        done();
      });
    });

    it('should generate valid ISO timestamp', (done) => {
      // Arrange
      const testData = { test: 'data' };
      mockCallHandler.handle = jest.fn().mockReturnValue(of(testData));

      // Act
      const result = interceptor.intercept(
        mockExecutionContext as ExecutionContext,
        mockCallHandler as CallHandler,
      );

      // Assert
      result.subscribe((transformedData: Response<any>) => {
        const timestamp = transformedData.timestamp;
        expect(timestamp).toBeDefined();
        expect(typeof timestamp).toBe('string');
        
        // Validate ISO format
        const date = new Date(timestamp);
        expect(date.toISOString()).toBe(timestamp);
        
        // Should be recent (within last second)
        const now = new Date();
        const diff = now.getTime() - date.getTime();
        expect(diff).toBeLessThan(1000);
        
        done();
      });
    });

    it('should handle complex nested objects', (done) => {
      // Arrange
      const testData = {
        user: {
          id: 1,
          profile: {
            name: 'John Doe',
            settings: {
              theme: 'dark',
              notifications: true,
            },
          },
        },
        metadata: {
          version: '1.0.0',
          timestamp: '2023-01-01',
        },
      };
      mockCallHandler.handle = jest.fn().mockReturnValue(of(testData));

      // Act
      const result = interceptor.intercept(
        mockExecutionContext as ExecutionContext,
        mockCallHandler as CallHandler,
      );

      // Assert
      result.subscribe((transformedData: Response<any>) => {
        expect(transformedData.data).toEqual(testData);
        expect(transformedData.statusCode).toBe(200);
        expect(transformedData.timestamp).toBeDefined();
        done();
      });
    });

    it('should not modify the original data', (done) => {
      // Arrange
      const testData = { id: 1, name: 'test' };
      const originalData = { ...testData };
      mockCallHandler.handle = jest.fn().mockReturnValue(of(testData));

      // Act
      const result = interceptor.intercept(
        mockExecutionContext as ExecutionContext,
        mockCallHandler as CallHandler,
      );

      // Assert
      result.subscribe((transformedData: Response<any>) => {
        expect(testData).toEqual(originalData);
        expect(transformedData.data).toEqual(testData);
        expect(transformedData.data).not.toBe(testData); // Should not be the same reference
        done();
      });
    });
  });
});
