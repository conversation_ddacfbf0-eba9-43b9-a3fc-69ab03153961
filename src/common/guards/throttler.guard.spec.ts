import { Test, TestingModule } from '@nestjs/testing';
import { ThrottlerBehindProxyGuard } from './throttler.guard';

describe('ThrottlerBehindProxyGuard', () => {
  let guard: ThrottlerBehindProxyGuard;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ThrottlerBehindProxyGuard],
    }).compile();

    guard = module.get<ThrottlerBehindProxyGuard>(ThrottlerBehindProxyGuard);
  });

  it('should be defined', () => {
    expect(guard).toBeDefined();
  });

  describe('getTracker', () => {
    it('should return the first IP from ips array when available', async () => {
      // Arrange
      const mockRequest = {
        ips: ['***********', '********'],
        ip: '127.0.0.1',
      };

      // Act
      const result = await guard['getTracker'](mockRequest);

      // Assert
      expect(result).toBe('***********');
    });

    it('should return the direct IP when ips array is empty', async () => {
      // Arrange
      const mockRequest = {
        ips: [],
        ip: '127.0.0.1',
      };

      // Act
      const result = await guard['getTracker'](mockRequest);

      // Assert
      expect(result).toBe('127.0.0.1');
    });

    it('should return the direct IP when ips array is not present', async () => {
      // Arrange
      const mockRequest = {
        ip: '***********00',
      };

      // Act
      const result = await guard['getTracker'](mockRequest);

      // Assert
      expect(result).toBe('***********00');
    });

    it('should handle multiple IPs in ips array and return the first one', async () => {
      // Arrange
      const mockRequest = {
        ips: ['***********', '************', '*********'],
        ip: '127.0.0.1',
      };

      // Act
      const result = await guard['getTracker'](mockRequest);

      // Assert
      expect(result).toBe('***********');
    });

    it('should handle IPv6 addresses', async () => {
      // Arrange
      const mockRequest = {
        ips: ['2001:db8::1'],
        ip: '::1',
      };

      // Act
      const result = await guard['getTracker'](mockRequest);

      // Assert
      expect(result).toBe('2001:db8::1');
    });

    it('should fallback to direct IP for IPv6 when no ips array', async () => {
      // Arrange
      const mockRequest = {
        ips: [],
        ip: '::1',
      };

      // Act
      const result = await guard['getTracker'](mockRequest);

      // Assert
      expect(result).toBe('::1');
    });

    it('should handle undefined ips property', async () => {
      // Arrange
      const mockRequest = {
        ip: '********',
      };

      // Act
      const result = await guard['getTracker'](mockRequest);

      // Assert
      expect(result).toBe('********');
    });

    it('should handle null ips property', async () => {
      // Arrange
      const mockRequest = {
        ips: null,
        ip: '**********',
      };

      // Act
      const result = await guard['getTracker'](mockRequest);

      // Assert
      expect(result).toBe('**********');
    });

    it('should return a promise that resolves to the tracker', () => {
      // Arrange
      const mockRequest = {
        ips: ['***********'],
        ip: '127.0.0.1',
      };

      // Act
      const result = guard['getTracker'](mockRequest);

      // Assert
      expect(result).toBeInstanceOf(Promise);
      return expect(result).resolves.toBe('***********');
    });

    it('should handle edge case with single IP in ips array', async () => {
      // Arrange
      const mockRequest = {
        ips: ['*************'],
        ip: '127.0.0.1',
      };

      // Act
      const result = await guard['getTracker'](mockRequest);

      // Assert
      expect(result).toBe('*************');
    });
  });
});
